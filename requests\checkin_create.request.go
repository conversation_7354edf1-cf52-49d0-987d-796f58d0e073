package requests

import (
	"fmt"
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CheckinCreateItem struct {
	Type     *string `json:"type"`
	Period   *string `json:"period"`
	Location *string `json:"location"`
	Remarks  *string `json:"remarks"`
	IsUnused *bool   `json:"is_unused"`
}

type CheckinCreate struct {
	core.BaseValidator
	Items *[]CheckinCreateItem `json:"items"`
}

func (r *CheckinCreate) Valid(ctx core.IContext) core.IError {
	// Validate that items array is not empty
	if r.Must(r.IsRequired(r.Items, "items")) && r.Must(r.Is<PERSON>y<PERSON>in(*r.Items, 1, "items")) {
		// Validate each item in the array
		for i, item := range *r.Items {
			fieldPrefix := fmt.Sprintf("items[%d]", i)

			// Validate type
			r.Must(r.IsStrIn(item.Type, strings.Join([]string{string(models.CheckinTypeOfficeHQ), string(models.CheckinTypeWfh),
				string(models.CheckinTypeOnsite), string(models.CheckinTypeOfficeAKV),
				string(models.CheckinTypeBusinessTrip), string(models.CheckinTypeAnnual),
				string(models.CheckinTypeSick), string(models.CheckinTypeMenstrual),
				string(models.CheckinTypeBirthday), string(models.CheckinTypeOrdination),
				string(models.CheckinTypeBusiness)}, "|"), fieldPrefix+".type"))

			if r.Must(r.IsStrRequired(item.Type, fieldPrefix+".type")) {
				// Validate location for specific types
				if utils.ToNonPointer(item.Type) == string(models.CheckinTypeOnsite) || utils.ToNonPointer(item.Type) == string(models.CheckinTypeBusinessTrip) {
					r.Must(r.IsStrRequired(item.Location, fieldPrefix+".location"))
				}

				// Validate leave period for leave types
				if utils.ToNonPointer(item.Type) == string(models.CheckinTypeAnnual) || utils.ToNonPointer(item.Type) == string(models.CheckinTypeSick) ||
					utils.ToNonPointer(item.Type) == string(models.CheckinTypeMenstrual) ||
					utils.ToNonPointer(item.Type) == string(models.CheckinTypeBirthday) ||
					utils.ToNonPointer(item.Type) == string(models.CheckinTypeOrdination) ||
					utils.ToNonPointer(item.Type) == string(models.CheckinTypeBusiness) {
					r.Must(r.IsStrRequired(item.Period, fieldPrefix+".period"))
					r.Must(r.IsStrIn(item.Period, strings.Join([]string{string(models.CheckinPeriodHalfMorning), string(models.CheckinPeriodHalfAfternoon),
						string(models.CheckinPeriodFullDay), string(models.CheckinPeriodManyDays)}, "|"), fieldPrefix+".period"))
				}
			}
		}
	}

	return r.Error()
}
